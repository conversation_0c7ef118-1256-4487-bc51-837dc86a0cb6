/**
 * 地图配置类型定义
 */
export interface MapConfig {
  /** 地图中心点坐标数组 [经度, 纬度] */
  mapCenter: number[];
  /** 地图中心点对象 */
  mapCenterObj: { x: number; y: number; z: number };
  /** 地图中心点字符串对象（包含roll角度） */
  mapCenterStr: { x: number; y: number; z: number; roll: number };
  /** 天地图API密钥 */
  mapKey: string;
}

/**
 * 地图配置对象
 * 包含地图初始化所需的基本配置信息
 */
const mapConfig: MapConfig = {
  mapCenter: [102.365944, 24.941118], // 地图中心点坐标数组
  mapCenterObj: { x: 102.365944, y: 24.941118, z: 0 }, // 地图中心点对象
  mapCenterStr: { x: 102.365944, y: 24.941118, z: 2000, roll: 1 }, // 地图中心点字符串对象
  mapKey: '400f431fbfcbfda6904e20023f88743c', // 天地图API密钥
};

export default mapConfig;
