import { inspectionRequest } from '@/request';
import downloadBlob from '@/service/downloadBlob';
import { BaseResponse } from '@/types/common';

import {
  TaskVo,
  TaskInsertParam,
  TaskPageQueryParam,
  TaskPageResponse,
  TaskUpdateParam,
} from '@/types/task';

export default {
  /**
   * @param data InsertParam 待新增的任务信息
   * @returns 新增结果 || null
   * @description 新增
   */
  insert: async (data: TaskInsertParam) => {
    return inspectionRequest.post<BaseResponse<object>>(`/task/insert`, { data });
  },
  /**
   * @param data updateParam 待更新的任务信息
   * @returns 更新结果 || null
   * @description 修改
   */
  update: async (data: TaskUpdateParam) => {
    return inspectionRequest.put<BaseResponse<object>>(`/task/update`, { data });
  },
  /**
   * @param id 任务id
   * @returns 删除结果 || null
   * @description deleteById
   */
  deleteById: async (id: string) => {
    return inspectionRequest.delete<BaseResponse<object>>(`/task/deleteById`, {
      params: { id },
    });
  },
  /**
   * @param data {id:''} 任务id
   * @returns 查询结果 || null
   * @description 根据id查询任务信息
   */
  getDetailById: async (data: { id: string }) => {
    return inspectionRequest.get<BaseResponse<TaskVo>>(`/task/getById`, {
      params: data,
    });
  },
  /**
   * @param data PageQueryParam 分页查询参数
   * @returns  任务信息列表 || null
   * @description 获取列表
   */
  queryByPage: async (data: TaskPageQueryParam) => {
    return inspectionRequest.post<TaskPageResponse>(`/task/queryByPage`, { data });
  },
  /**
   * @param data PageQueryParam 分页查询参数
   * @returns  任务信息导出文件 || null
   * @description 导出列表
   */
  exportByPage: async (data: TaskPageQueryParam) => {
    return new Promise((resolve) => {
      downloadBlob('/task/exportByPage', {
        method: 'POST',
        data,
      }).finally(() => {
        resolve('finally');
      });
    });
  },
};
