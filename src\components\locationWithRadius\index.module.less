.content {
	width: 100%;
	height: 100vh;
}

.location-select-container {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;

	.map-select-input {
		height: 100%;
	}

	.map-select-btn {
		margin-left: auto;
		margin-right: 5px;
		height: 100%;
		text-align: center;
		font-size: 10px;
	}
}


.map-locationradiusmodal-content {
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 660px;

	.map-area {
		width: 100%;
		height: 100%;
	}

	.tips {
		color: red;
		padding-top: 10px;
	}

	.footer {
		margin-top: 20px;
		width: 100%;
		display: flex;
		flex-direction: row-reverse;

		.confirm-btn {
			margin-left: auto;
			margin-right: 10px;
			background-color: #007EBB;
		}

		.location-selector-cancel-btn {
			margin-right: 20px;
		}
	}
}

.handle-tag {
	margin-right: 10px;
	background-color: #007ebb;
	color: #FFF;
}
