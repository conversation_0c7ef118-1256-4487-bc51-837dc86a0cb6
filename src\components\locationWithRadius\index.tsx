/**
 * 地图位置选择组件
 * 支持地图标点、绘制多边形、查看位置等功能
 * 可配置覆盖半径，支持多种操作模式
 */
import React, { useState, useEffect, useRef } from 'react';
import baseApi, { ByParkCodeType } from '@/service/baseApi';
import { Button, Input, Modal, Spin, message } from 'antd';
import mapConfig from '@/components/mapUtils/mapConfig';
import style from './index.module.less';
import MapCard, { MapCardExpose } from '../MapCard';

/**
 * 操作类型枚举
 * - edit: 编辑模式，可修改现有位置
 * - add: 新增模式，选择新位置
 * - view: 查看模式，只读显示
 * - draw: 绘制模式，手绘多边形
 * - drawpolygon: 多边形预览模式
 */
type OperateType = 'edit' | 'add' | 'view' | 'draw' | 'drawpolygon';

/**
 * 坐标点类型
 * 用于表示经纬度坐标
 */
interface CoordinatePoint {
  lng: number; // 经度
  lat: number; // 纬度
}

/**
 * 位置类型 - 支持两种格式
 * - 数组格式: [经度, 纬度]
 * - 对象格式: {x: 经度, y: 纬度, z: 高度}
 */
type Position = [number, number] | { x: number; y: number; z: number };

/**
 * 地图半径值类型
 * 包含中心点坐标和半径大小
 */
interface RadiusValue {
  x: number; // 中心点X坐标（经度）
  y: number; // 中心点Y坐标（纬度）
  r: number; // 半径大小（米）
}

/**
 * 地图引用类型
 * 扩展MapCardExpose以支持额外的地图操作方法
 */
interface MapRef extends MapCardExpose {
  getCamera?: () => unknown; // 获取相机信息
  getMapCenterAndRadius: () => RadiusValue; // 获取地图中心点和半径
  drawPolygon?: (isFirst: boolean) => void; // 绘制多边形
  clearPolygons?: () => void; // 清空多边形
  getDrawedPolygons?: () => unknown; // 获取已绘制的多边形
}

/**
 * 组件Props类型定义
 */
interface LocationWithRadiusProps {
  operateType: OperateType; // 操作模式
  radius: number; // 覆盖半径（米）
  value?: string; // 初始值，可以是坐标字符串或多边形JSON
  confirmClick?: (radiusVal: RadiusValue) => void; // 确认选点回调
  drawConfirm?: (polygons: string) => void; // 确认绘制多边形回调
  onChange?: (value: string) => void; // 值变化回调
}

/**
 * 地图位置选择组件
 *
 * @param operateType - 操作模式：edit/add/view/draw/drawpolygon
 * @param radius - 覆盖半径大小（米）
 * @param value - 初始值，坐标字符串或多边形JSON
 * @param confirmClick - 确认选点时的回调函数
 * @param drawConfirm - 确认绘制多边形时的回调函数
 * @param onChange - 值变化时的回调函数
 */
const LocationWithRadius: React.FC<LocationWithRadiusProps> = ({
  operateType,
  radius,
  value = '',
  confirmClick = () => {},
  drawConfirm = () => {},
  onChange = () => {},
}): JSX.Element => {
  // ==================== 状态管理 ====================

  /** 控制地图弹窗的显示状态 */
  const [isShowAddObjectModal, setIsShowAddObjectModal] = useState<boolean>(false);

  /** 地图选点坐标（暂未使用，保留用于扩展） */
  const [, setmapselectCoord] = useState<number[]>([]);

  /** 当前地图位置，支持数组和对象两种格式 */
  const [position, setPosition] = useState<Position>([0, 0]);

  /** 输入框显示的值 */
  const [inputValue, setInputValue] = useState<string>('');

  /** 标记是否正在绘制多边形 */
  const [isDrawing, setIsDrawing] = useState<boolean>(false);

  /** 当前设置的覆盖半径值 */
  const [radiusValue, setRadiusValue] = useState<number>(0);

  /** 地图组件的引用，用于调用地图方法 */
  const tmapRef: React.MutableRefObject<MapRef | null> = useRef<MapRef | null>(null);

  // ==================== 业务逻辑函数 ====================

  /**
   * 查询地图默认中心点
   * 从后端API获取园区中心坐标，如果获取失败则使用默认中心点
   */
  const queryMapCenter: () => Promise<void> = async (): Promise<void> => {
    const result: ByParkCodeType = await baseApi.queryByParkCode();
    const centerData: Position = result?.center
      ? JSON.parse(result.center)
      : mapConfig.mapCenterObj;
    setPosition(centerData);
  };

  /**
   * 监听props变化，初始化组件状态
   * 根据不同的操作模式和传入的值，设置地图位置和相关状态
   */
  useEffect((): void => {
    // 同步输入框显示值
    setInputValue(value);

    if (value && value !== '') {
      // 根据操作模式处理传入的值
      if (operateType.includes('draw')) {
        // 绘制模式：解析多边形JSON数据
        try {
          const data: CoordinatePoint[][] = JSON.parse(value);
          if (data.length > 0 && data[0].length > 0) {
            // 使用多边形第一个点作为地图中心
            const newPosition: [number, number] = [data[0][0].lng, data[0][0].lat];
            setPosition(newPosition);
          }
        } catch {
          // JSON解析失败，使用默认位置
          const fallbackPosition: [number, number] = [0, 0];
          setPosition(fallbackPosition);
        }
      } else {
        // 标点模式：解析坐标字符串 "经度,纬度"
        const locationList: string[] = String(value).split(',');
        if (locationList.length === 2) {
          const newPosition: { x: number; y: number; z: number } = {
            x: parseFloat(locationList[0]),
            y: parseFloat(locationList[1]),
            z: 0,
          };
          setPosition(newPosition);
          // 初始化半径值
          if (radiusValue === 0) {
            setRadiusValue(radius);
          }
        } else {
          // 坐标格式不正确，使用默认位置
          const fallbackPosition: [number, number] = [0, 0];
          setPosition(fallbackPosition);
        }
      }
    } else if (operateType === 'view') {
      // 查看模式且无坐标数据：设置为无效位置，禁止显示地图
      const viewPosition: [number, number] = [0, 0];
      setPosition(viewPosition);
    } else {
      // 其他模式且无初始值：查询默认地图中心
      queryMapCenter();
    }
  }, [value, operateType, radius, radiusValue]);

  // ==================== 地图操作函数 ====================

  /**
   * 开始绘制多边形
   * @param isFirst - 是否为首次绘制
   */
  const draw: (isFirst: boolean) => void = (isFirst: boolean): void => {
    tmapRef.current?.drawPolygon?.(isFirst);
  };

  /**
   * 清空已绘制的多边形并重新开始绘制
   */
  const clearPolygons: () => void = (): void => {
    tmapRef.current?.clearPolygons?.();
    setIsDrawing(false);
    draw(true);
  };

  /**
   * 关闭地图弹窗
   * 重置绘制状态并隐藏弹窗
   */
  const closeAddModal: () => void = (): void => {
    setIsDrawing(false);
    setIsShowAddObjectModal(false);
  };

  /**
   * 保存绘制的多边形
   * 获取已绘制的多边形数据并通过回调函数返回
   */
  const save: () => void = (): void => {
    const polygons: unknown = tmapRef.current?.getDrawedPolygons?.();
    if (polygons) {
      const polygonsString: string = JSON.stringify(polygons);
      drawConfirm(polygonsString);
    }
    closeAddModal();
  };

  /**
   * 确认位置选择
   * 获取地图中心点和半径信息，通过回调函数返回
   */
  const confirmLocationSelect: () => void = (): void => {
    const radiusVal: RadiusValue | undefined = tmapRef.current?.getMapCenterAndRadius();
    if (radiusVal) {
      confirmClick(radiusVal);
      setRadiusValue(radiusVal.r);
      const coordString: string = `${radiusVal.x},${radiusVal.y}`;
      onChange?.(coordString);
    }
    setIsShowAddObjectModal(false);
  };

  /**
   * 显示地图弹窗
   * 检查是否有有效坐标数据，决定是否显示地图或提示无数据
   */
  const showMap: () => void = (): void => {
    const isViewMode: boolean = operateType === 'view';
    const isArrayPosition: boolean = Array.isArray(position);
    let isZeroPosition: boolean = false;

    // 检查是否为无效位置（0,0坐标）
    if (isArrayPosition) {
      const arrayPosition: [number, number] = position as [number, number];
      isZeroPosition = arrayPosition[0] === 0 && arrayPosition[1] === 0;
    }

    // 查看模式下如果没有坐标数据则提示，否则显示地图弹窗
    if (isViewMode && isZeroPosition) {
      message.info('无坐标数据');
    } else {
      setIsShowAddObjectModal(true);
    }
  };

  /**
   * 地图坐标变化回调
   * 当地图上的坐标发生变化时触发（暂未使用，保留用于扩展）
   */
  const onCoordChange: (val: number[]) => void = (val: number[]): void => {
    setmapselectCoord(val);
  };

  // ==================== 渲染逻辑 ====================

  /** 根据操作模式确定按钮文本 */
  const buttonText: string =
    operateType === 'add' || operateType === 'edit' || operateType === 'draw' ? '地图拾取' : '查看';

  return (
    <div className={style['location-select-container']}>
      {/* 坐标显示输入框 - 只读，显示当前选择的坐标 */}
      <Input disabled className={style['map-select-input']} value={inputValue} />

      {/* 地图操作按钮 - 根据操作模式显示不同文本 */}
      <Button className={style['map-select-btn']} type="primary" size="small" onClick={showMap}>
        {buttonText}
      </Button>

      {/* 地图选择弹窗 */}
      <Modal
        title="标绘位置"
        width="60vw"
        destroyOnClose
        visible={isShowAddObjectModal}
        footer={null}
        onCancel={closeAddModal}
        maskClosable={false}
      >
        <Spin spinning={false}>
          <div className={style['map-locationradiusmodal-content']}>
            {/* 地图显示区域 */}
            <div className={style['map-area']}>
              <MapCard
                onRef={tmapRef}
                onCoordChange={onCoordChange}
                mapConfig={{ satellite: true }} // 启用卫星图层
                position={position}
                radius={radiusValue}
                operateType={operateType}
              />
            </div>

            {/* 操作按钮区域 */}
            <div className={style.footer}>
              {/* 取消按钮 - 所有模式都显示 */}
              <Button className={style['location-selector-cancel-btn']} onClick={closeAddModal}>
                取消
              </Button>

              {/* 绘制模式的操作按钮组 */}
              {operateType === 'draw' && (
                <div>
                  {/* 开始绘制按钮 */}
                  <Button
                    className={style['confirm-btn']}
                    type="primary"
                    onClick={() => {
                      if (!isDrawing) {
                        draw(true);
                        setIsDrawing(true);
                      } else {
                        message.info('开始绘制');
                      }
                    }}
                  >
                    绘制企业位置
                  </Button>

                  {/* 清空重绘按钮 */}
                  <Button
                    className={style['confirm-btn']}
                    type="primary"
                    onClick={() => {
                      clearPolygons();
                    }}
                  >
                    清空重绘
                  </Button>

                  {/* 保存绘制结果按钮 */}
                  <Button
                    className={style['confirm-btn']}
                    type="primary"
                    onClick={() => {
                      save();
                    }}
                  >
                    保存
                  </Button>
                </div>
              )}

              {/* 标点模式的确定按钮 */}
              {(operateType === 'add' || operateType === 'edit') && (
                <Button
                  className={style['confirm-btn']}
                  onClick={confirmLocationSelect}
                  type="primary"
                >
                  确定
                </Button>
              )}
            </div>
          </div>
        </Spin>
      </Modal>
    </div>
  );
};

export default LocationWithRadius;
