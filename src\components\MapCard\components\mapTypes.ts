/**
 * 字符串或数字类型
 * 用于表示可以是字符串或数字的值，如坐标、ID等
 */
export type SORN = string | number;

/**
 * 地址组件接口
 * 用于表示详细的地址信息结构
 */
export interface Address {
  /** 地址组件详细信息 */
  addressComponent: {
    /** 城市代码 */
    citycode: string;
    /** 行政区划代码 */
    adcode: string;
    /** 商圈列表 */
    businessAreas: string[];
    /** 社区类型 */
    neighborhoodType: string;
    /** 社区名称 */
    neighborhood: string;
    /** 省份 */
    province: string;
    /** 街道 */
    street: string;
    /** 门牌号 */
    streetNumber: string;
    /** 乡镇 */
    township: string;
  };
  /** 交叉路口列表 */
  crosses: string[];
  /** 格式化地址 */
  formattedAddress: string;
  /** 兴趣点列表 */
  pois: string[];
  /** 道路列表 */
  roads: string[];
}

/**
 * 坐标变化属性接口
 * 用于传递坐标变化时的相关信息
 */
export interface CoordChangeProps {
  /** 经度 */
  lng: SORN;
  /** 纬度 */
  lat: SORN;
  /** 位置坐标数组 [lng, lat, alt] */
  position?: SORN[];
  /** 详细地址信息 */
  address?: Address;
  /** 格式化地址字符串 */
  formattedAddress?: string;
}

/**
 * 多边形数据接口
 * 用于表示绘制的多边形区域数据
 */
export interface PolygonData {
  /** 多边形顶点坐标列表 */
  list: number[];
  /** 多边形颜色 */
  color: string;
}

/**
 * 地图位置接口
 * 用于表示地图上的位置坐标
 */
export interface MapPosition {
  /** X坐标（经度） */
  x: number;
  /** Y坐标（纬度） */
  y: number;
  /** Z坐标（高度），可选 */
  z?: number;
}

/**
 * 地图相机配置接口
 * 用于表示地图的视角和相机参数
 */
export interface CameraConfig {
  /** 相机位置 */
  position?: MapPosition;
  /** 相机朝向 */
  direction?: MapPosition;
  /** 相机上方向 */
  up?: MapPosition;
  /** 视角范围 */
  fov?: number;
}

/**
 * 操作类型
 * 定义地图组件支持的操作模式
 */
export type OperateType = 'edit' | 'add' | 'view' | 'draw' | 'drawpolygon';

/**
 * 地图卡片组件暴露的方法接口
 * 定义了父组件可以调用的地图操作方法
 */
export interface MapCardExpose {
  /** 重置地图到初始状态 */
  resetMap: (posClear?: boolean) => void;
  /** 销毁地图实例 */
  destroyMap: () => void;
  /** 获取当前绘制的区域数据 */
  getDrawData?: () => PolygonData[];
}

/**
 * 地图卡片组件属性接口
 * 定义了地图组件接受的所有属性
 */
export interface Props {
  /** 地图配置选项 */
  mapConfig?: {
    /** 地图宽度 */
    width?: string;
    /** 地图高度 */
    height?: string;
    /** 地图中心坐标 [lng, lat] */
    center?: number[];
    /** 地图缩放层级 */
    zoom?: number;
    /** 是否开启卫星图 */
    satellite?: boolean;
  };
  /** 3D图层加载配置 */
  load3DLayer?: string;
  /** 相机配置 */
  camera?: CameraConfig;
  /** 区域列表数据，JSON字符串格式 */
  areaList?: string;
  /** 位置坐标，支持数组、对象或空对象格式 */
  position?: SORN[] | MapPosition | Record<string, never>;
  /** 坐标变化回调函数 */
  onCoordChange?: (props: string | CoordChangeProps | number[]) => void;
  /** 操作类型 */
  operateType: OperateType;
  /** 确认按钮回调函数 */
  confirmButton?: (props: unknown) => void;
  /** 取消按钮回调函数 */
  cancelButton?: () => void;
  /** 半径配置，可以是数字或对象 */
  radius?: number | { value: number; unit: string };
  /** 组件引用，用于父组件调用子组件方法 */
  onRef?: React.MutableRefObject<MapCardExpose | null>;
}
