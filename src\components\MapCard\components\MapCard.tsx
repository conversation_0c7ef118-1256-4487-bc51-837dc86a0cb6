import { Map, TrackingLayer, PlotDraw, DrawHelper } from 'yth-map';
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Button, InputNumber, Spin, message } from 'antd';
import markImg from '@/assets/position.png';
import mapConfig from '@/components/mapUtils/mapConfig';
import style from './MapCard.module.less';
import tdt from '../images/tdt_img.jpg';

import { MapCardExpose, Props, SORN } from './mapTypes';

// 地图点位对象类型
interface PointObject {
  x: number;
  y: number;
  z: number;
}

// 地图圆形配置类型
interface CircleConfig {
  x: number;
  y: number;
  height: number;
  radius: number;
  showTips: boolean;
  clampToGround: boolean;
  fillColor: string;
  fill: boolean;
  outline: boolean;
  outlineWidth: number;
  outlineColor: string;
  displayCondition: {
    max: number;
  };
}
// 地图图层配置
const layers: Array<{
  name: string;
  image: string;
  show: boolean;
  list: Array<{
    url: string;
    layer: string;
    style: string;
    format: string;
    tileMatrixSetID: string;
    minimumLevel: number;
    maximumLevel: number;
    tilingScheme: number;
  }>;
}> = [
  {
    name: '天地图影像',
    image: tdt,
    show: true,
    list: [
      // 影像
      {
        url: 'http://t0.tianditu.com/img_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
        layer: 'img',
        style: 'default',
        format: 'image/png',
        tileMatrixSetID: 'c',
        minimumLevel: 1,
        maximumLevel: 18,
        tilingScheme: 2,
      },
      // 影像注记
      {
        url: 'http://t0.tianditu.com/cia_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
        layer: 'cia',
        style: 'default',
        format: 'image/png',
        tileMatrixSetID: 'c',
        minimumLevel: 1,
        maximumLevel: 18,
        tilingScheme: 2,
      },
    ],
  },
  {
    name: '天地图(矢量)',
    image: tdt,
    show: false,
    list: [
      // 矢量
      {
        url: 'http://t0.tianditu.com/vec_c/wmts?tk=52fea9a68c4c16c39dd759ab4fce602f&service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
        layer: 'vec',
        style: 'default',
        format: 'image/png',
        tileMatrixSetID: 'c',
        minimumLevel: 1,
        maximumLevel: 18,
        tilingScheme: 2,
      },
      // 矢量注记
      {
        url: 'http://t0.tianditu.com/cva_c/wmts?tk=52fea9a68c4c16c39dd759ab4fce602f&service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
        layer: 'cva',
        style: 'default',
        format: 'image/png',
        tileMatrixSetID: 'c',
        minimumLevel: 1,
        maximumLevel: 18,
        tilingScheme: 2,
      },
    ],
  },
];
// 防抖Hook - 用于延迟处理频繁变化的值
const useDebounce: <T>(value: T, delay?: number) => T = <T,>(value: T, delay?: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect((): (() => void) => {
    const timeout: NodeJS.Timeout = setTimeout((): void => setDebouncedValue(value), delay);
    return (): void => clearTimeout(timeout);
  }, [value, delay]);

  return debouncedValue;
};
// 默认Props配置
const propsDefault: Props = {
  mapConfig: {
    width: '100%',
    height: '100%',
    satellite: false,
    zoom: 10,
  },
  position: {},
  operateType: 'add',
  radius: 0,
};

// 全局地图实例
const map: Map = new Map();

// MapCard组件 - 地图卡片组件，支持地图显示、标点、绘制等功能
const MapCard: React.ForwardRefExoticComponent<Props & React.RefAttributes<MapCardExpose>> =
  forwardRef<MapCardExpose, Props>((props: Props, ref): JSX.Element => {
    // 地图点位标记状态
    const [position, setPosition] = useState<SORN[]>(['', '', '']);

    // 覆盖半径圆形图层控制器
    const circleLayerController: React.MutableRefObject<TrackingLayer | null> =
      useRef<TrackingLayer | null>(null);

    // 覆盖半径数值状态
    const [radiusValue, setRadiusValue] = useState<number>(0);

    // 覆盖半径数值引用（用于实时获取最新值）
    const radiusValueRef: React.MutableRefObject<number> = useRef<number>(0);

    // 地图初始化加载状态
    const [initLoading, setInitLoading] = useState<boolean>(false);

    // 转换位置坐标为地图点位对象
    const transferPoint: (value: SORN[]) => PointObject = (value: SORN[]): PointObject => {
      const pointObj: PointObject = {
        x: Number(value[0]),
        y: Number(value[1]),
        z: Number(value[2]) || 1,
      };
      return pointObj;
    };

    // 在地图上绘制覆盖半径圆形
    const mapDrawCircle: (radius: number) => void = (radius: number): void => {
      if (!circleLayerController.current) return;

      circleLayerController.current.clearAll();

      const circleConfig: CircleConfig = {
        x: Number(position[0]),
        y: Number(position[1]),
        height: 0,
        radius,
        showTips: false,
        clampToGround: true,
        fillColor: 'rgba(64,152,252,0.3)',
        fill: true,
        outline: true,
        outlineWidth: 1,
        outlineColor: 'rgba(64,152,252,1)',
        displayCondition: {
          max: 2000,
        },
      };

      circleLayerController.current.addCircle(circleConfig);
    };

    // 在地图上标记位置点
    const markPositon: (value: PointObject) => void = (value: PointObject): void => {
      if (!map || !map.layer) return;

      map.layer.clearAll();

      // 添加位置标记
      map.layer.addMarker({
        layerName: 'positionSele',
        point: value,
        img: markImg,
        scale: 1,
        offset: { x: 0, y: -15 },
      });

      // 如果有半径值，绘制覆盖圆形
      if (radiusValueRef.current && radiusValueRef.current !== 0) {
        mapDrawCircle(radiusValueRef.current);
      }
    };

    // 激活地图位置选择功能
    const selectPosition: () => void = (): void => {
      if (!map || !map.layer || !map.plotDraw) return;

      setPosition([]);
      map.layer.clearAll();

      map.plotDraw.activate(0, {
        callback: (geo: { geometry: { coordinates: SORN[][][] } }): void => {
          const coordinates: SORN[] = geo.geometry.coordinates[0][0];
          setPosition(coordinates);
          markPositon(transferPoint(coordinates));
        },
      });
    };

    // 重置地图到初始状态
    const resetMap: (posClear?: boolean) => void = (posClear: boolean = false): void => {
      setInitLoading(false);
      if (props.position && !posClear) return;
      setPosition(['', '', '']);
      if (map) {
        map.resetPlace();
      }
    };

    // 销毁地图实例
    const destroyMap: () => void = (): void => {
      resetMap();
      if (map) {
        map.resetPlace();
      }
    };

    // 获取地图中心点和半径信息
    const getMapCenterAndRadius: () => { x: SORN; y: SORN; r: number } = () => {
      return {
        x: position[0],
        y: position[1],
        r: radiusValueRef.current,
      };
    };

    // 获取地图相机信息
    const getCamera: () => unknown = (): unknown => {
      if (!map) return null;
      return map.getCameraInfo();
    };

    // 处理本地搜索结果
    const localSearchResult: (result: {
      pois?: Array<{ name: string; lonlat: string }>;
    }) => void = (result: { pois?: Array<{ name: string; lonlat: string }> }): void => {
      const resultsList: HTMLElement | null = document.getElementById('results');
      if (!resultsList) return;

      if (result && result.pois && Array.isArray(result.pois)) {
        const locations: Array<{ name: string; lonlat: string }> = result.pois;

        locations.forEach((location: { name: string; lonlat: string }, index: number): void => {
          const listItem: HTMLLIElement = document.createElement('li');
          listItem.textContent = `${index + 1}: ${location.name}`;

          listItem.addEventListener('click', (): void => {
            const coordinates: string[] = location.lonlat.split(',');
            if (map && coordinates.length >= 2) {
              map.flyObject({
                x: parseFloat(coordinates[0]),
                y: parseFloat(coordinates[1]),
                z: 155,
              });
            }
          });

          resultsList.appendChild(listItem);
        });
      } else {
        message.error('无数据');
      }
    };

    // 根据关键字搜索地点
    const searchLocation: () => Promise<void> = async (): Promise<void> => {
      const resultsList: HTMLElement | null = document.getElementById('results');
      const inputElement: HTMLInputElement | null = document.getElementById(
        'inputSearch',
      ) as HTMLInputElement;

      if (!resultsList || !inputElement) return;

      resultsList.innerHTML = '';
      const searchValue: string = inputElement.value;

      if (!searchValue.trim()) return;

      const searchUrl: string = `https://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"${searchValue}","level":"11","mapBound":"102.546150,24.396308,103.157679,25.132221","queryType":"1","count":"10","start":"0"}&type=query&tk=${mapConfig.mapKey}`;

      try {
        const response: Response = await fetch(searchUrl, {
          referrerPolicy: 'strict-origin-when-cross-origin',
          method: 'GET',
          mode: 'cors',
          credentials: 'omit',
        });

        const data: { pois?: Array<{ name: string; lonlat: string }> } = await response.json();
        localSearchResult(data);
      } catch {
        message.error('搜索失败，请重试');
      }
    };

    // 清空搜索结果
    const clearSearch: () => void = (): void => {
      const searchInput: HTMLInputElement | null = document.getElementById(
        'inputSearch',
      ) as HTMLInputElement;
      const resultsList: HTMLElement | null = document.getElementById('results');

      if (searchInput) searchInput.value = '';
      if (resultsList) resultsList.innerHTML = '';
    };

    // 初始化地图实例和配置
    const initMap: () => Promise<void> = async (): Promise<void> => {
      try {
        setInitLoading(true);

        // 设置初始地图中心点
        let initPoint: PointObject = mapConfig.mapCenterStr ?? { x: 0, y: 0, z: 0 };

        // 如果传入了位置参数，使用传入的位置
        if (props.position && typeof props.position === 'object' && 'x' in props.position) {
          initPoint = props.position as PointObject;
          setPosition([
            props.position.x,
            props.position.y,
            props.position.z,
            (props.position as PointObject & { roll?: number }).roll || 0,
          ]);
          setRadiusValue(Number(props.radius) || 0);
          radiusValueRef.current = Number(props.radius) || 0;
        } else {
          setPosition([0, 0, 0, 0]);
        }

        // 地图初始化配置
        map.initMap({
          container: 'map',
          sceneModePicker: true,
          sceneModeChose: 3,
          positionDisplay: true,
          compassDisplay: true,
          hostAddr: 'http://***************:8096/check',
          components: true,
          rightClickCall: (): void => {},
          initPlace: {
            point: initPoint,
          },
          layersPro: layers,
          defaultView: {
            rect: [112.96100967885242, 28.194319720664925, 112.97098015969033, 28.198415260838136],
          },
          callback: (): void => {
            // 初始化圆形图层控制器
            circleLayerController.current = new TrackingLayer({ map });
            setInitLoading(false);

            // 处理相机配置
            if (props.camera && typeof props.camera === 'object') {
              let cameraObj: unknown = {};

              if (
                typeof props.camera === 'object' &&
                props.camera !== null &&
                'directionx' in props.camera
              ) {
                cameraObj = props.camera;
              } else if (typeof props.camera === 'string') {
                try {
                  cameraObj = JSON.parse(props.camera);
                } catch {
                  cameraObj = {};
                }
              }

              map.flyCamera(cameraObj, (): void => {
                if (props.load3DLayer && props.load3DLayer !== '') {
                  map.add3DTileLayer({
                    url: props.load3DLayer,
                    center: initPoint,
                  });
                }
              });
            } else {
              map.flyObject(initPoint);
            }

            // 设置地图背景和绘制工具
            map.setMapBackground('天地图影像');
            map.plotDraw = new PlotDraw({
              map,
              callback: (): void => {},
              editable: false,
            });

            // 初始化绘制辅助工具（仅初始化，不需要保存引用）
            // eslint-disable-next-line no-new
            new DrawHelper({ map });
          },
        });
      } catch {
        setInitLoading(false);
      }
    };

    // 暴露组件方法给父组件（通过ref）
    useImperativeHandle<MapCardExpose, MapCardExpose>(
      ref,
      (): MapCardExpose => ({
        resetMap,
        destroyMap,
      }),
    );

    // 暴露组件方法给父组件（通过onRef属性）
    useImperativeHandle(
      props.onRef,
      (): {
        resetMap: (posClear?: boolean) => void;
        destroyMap: () => void;
        getCamera: () => unknown;
        getMapCenterAndRadius: () => { x: SORN; y: SORN; r: number };
      } => ({
        resetMap,
        destroyMap,
        getCamera,
        getMapCenterAndRadius,
      }),
    );

    // 组件挂载时初始化地图
    useEffect((): void => {
      initMap();
    }, []);

    // 位置变化时更新地图标记和通知父组件
    useEffect((): void => {
      if (!position[0] || !position[1]) return;

      markPositon(transferPoint(position));

      // 避免重复触发
      if (props.position === position) return;

      // 通知父组件坐标变化
      if (props.onCoordChange) {
        const coordData: string = JSON.stringify({
          x: position[0],
          y: position[1],
          z: position[2],
          roll: 1,
        });
        props.onCoordChange(coordData);
      }
    }, [useDebounce(position, 200)]);

    // 监听props.position变化，更新内部状态
    useEffect((): void => {
      if (!map) return;
      if (Array.isArray(props.position)) {
        setPosition(props.position);
      }
    }, [props.position]);

    return (
      <Spin spinning={initLoading} size="large" wrapperClassName={style['spin-wrap']}>
        <main className={style['map-container']}>
          <div
            id="map"
            style={{
              width: props.mapConfig?.width || '100%',
              height: props.mapConfig?.height || '100%',
            }}
          />
          <div className={style.toolbar}>
            <div style={{ display: 'flex', alignItems: 'cneter' }}>
              <div className={`${style.info} ${style['ant-card']} ${style['ant-card-bordered']}`}>
                <div style={{ width: '100%', display: 'flex', alignItems: 'center' }}>
                  <div
                    style={{
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '10px',
                      margin: '10px',
                    }}
                  >
                    <div style={{ marginRight: '10px' }}>
                      <InputNumber
                        size="small"
                        addonBefore="经度"
                        value={position[0] ? `${position[0]}` : ''}
                        placeholder=""
                        readOnly
                      />
                    </div>
                    <div style={{ marginRight: '10px' }}>
                      <InputNumber
                        size="small"
                        addonBefore="纬度"
                        value={position[0] ? `${position[1]}` : ''}
                        placeholder=""
                        readOnly
                      />
                    </div>
                    <div style={{ marginRight: '10px' }}>
                      <InputNumber
                        size="small"
                        min={0}
                        addonAfter="米"
                        addonBefore="覆盖半径"
                        placeholder="请填写覆盖半径"
                        value={radiusValue}
                        onChange={(v: number | null): void => {
                          if (v && v > 0) {
                            setRadiusValue(v);
                            radiusValueRef.current = v;
                            mapDrawCircle(v);
                          }
                        }}
                      />
                    </div>
                    <div style={{ marginRight: '10px' }}>
                      {position.length > 0 && (
                        <Button
                          style={{ marginLeft: '10px' }}
                          size="small"
                          onClick={(): void => {
                            selectPosition();
                          }}
                          disabled={props.operateType === 'view'}
                          type="primary"
                        >
                          选择位置
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
                <div className={style.item}>
                  <div className={style['search-bar']}>
                    <div className={style['search-bar-input']}>
                      <input
                        id="inputSearch"
                        className={style.inputArea}
                        placeholder="请输入搜索内容"
                      />
                      <Button
                        type="default"
                        size="small"
                        className={style['map-search-btn']}
                        onClick={searchLocation}
                      >
                        搜索
                      </Button>
                      <Button
                        size="small"
                        className={style['map-clear-btn']}
                        type="default"
                        onClick={clearSearch}
                      >
                        重置
                      </Button>
                    </div>
                    <div className={style['search-bar-result']}>
                      <div id="results" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </Spin>
    );
  });

// 添加displayName
MapCard.displayName = 'MapCard';

// 设置默认值
MapCard.defaultProps = propsDefault;

export default MapCard;
