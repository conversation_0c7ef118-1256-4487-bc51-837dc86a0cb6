import React, { useState, useEffect } from 'react';
import { YTHForm } from 'yth-ui';
import planApi from '@/service/inspection/planApi';
import { message, <PERSON><PERSON>, Spin } from 'antd';
import baseApi from '@/service/baseApi';
import { formatTree } from 'yth-ui/es/components/util/treeList';
import { InspectionPlanVo, PlanInsertParam, PlanUpdateParam } from '@/types/inspection/plan';
import { BaseResponse } from '@/types/common';
import { CurrentUser } from '@/Constant';
import { Unit, User } from '@/service/system';
import locationPoint from '@/components/locationWithRadius';
import dicParams from '../dicParams';
import style from '../index.module.less';

/**
 * @description 弹窗参数类型定义
 */
type PropsTypes = {
  /** 弹窗的类别 add 新增 view 查看 edit 编辑 */
  type: string;
  /** 弹窗传入的数据 */
  dataObj: { id?: string; [key: string]: React.Key };
  /** 关闭弹窗的回调函数 */
  closeModal: () => void;
};

/**
 * @description 查看 或新增 modal
 * @param PropsTypes PropsTypes
 */
const InspectionPlanModal: React.FC<PropsTypes> = ({ type, dataObj, closeModal = () => {} }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isCasRequested, setIsCasRequested] = useState<boolean>(false);

  // 表单
  const form: ReturnType<typeof YTHForm.createForm> = React.useMemo(
    () => YTHForm.createForm({}),
    [],
  );

  // 查询详情
  const queryDataDetail: () => Promise<void> = async () => {
    setIsLoading(true);
    const res: BaseResponse<InspectionPlanVo> = await planApi.getDetailById({
      id: dataObj.id,
    });
    if (res && res.code && res.code === 200) {
      const formD: InspectionPlanVo = res.data;
      form.setValues(formD);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (type && (type === 'edit' || type === 'view')) {
      queryDataDetail().then(() => {
        setIsCasRequested(true);
      });
    } else {
      setIsCasRequested(true);
    }
  }, [type, dataObj]);

  // 点击取消
  const cancel: () => void = () => {
    form.reset();
    closeModal();
  };

  // 新增保存
  const submitAddData: (data: PlanInsertParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const res: BaseResponse<object> = await planApi.insert(data);
    if (res && res.code && res.code === 200) {
      message.success('新增数据成功');
      closeModal();
    } else {
      message.error('新增数据失败');
    }
    setIsLoading(false);
  };

  // 编辑保存
  const submitEditData: (data: PlanUpdateParam) => Promise<void> = async (data) => {
    setIsLoading(true);
    const res: BaseResponse<object> = await planApi.update({ ...data, id: dataObj?.id });
    if (res && res.code && res.code === 200) {
      message.success('更新数据成功');
      closeModal();
    } else {
      message.error('更新数据失败');
    }
    setIsLoading(false);
  };

  // 点击保存
  const save: () => Promise<void> = async () => {
    form.validate().then(() => {
      const submitData: PlanInsertParam = JSON.parse(JSON.stringify(form.values));
      if (type === 'add') {
        submitAddData(submitData);
      } else if (type === 'edit') {
        submitEditData(submitData);
      }
    });
  };

  // 删除数据dialog

  return (
    <div className={style['yth-inspection-moduel']}>
      <Spin spinning={isLoading}>
        {isCasRequested && (
          <YTHForm form={form} col={2}>
            <YTHForm.Item
              name="id"
              title="id"
              labelType={2}
              required={false}
              display="hidden"
              componentName="Input"
              componentProps={{
                disabled: true,
              }}
            />

            <YTHForm.Item
              name="planCode"
              title="计划编码"
              labelType={2}
              componentName="Input"
              componentProps={{
                disabled: true,
              }}
            />
            <YTHForm.Item
              name="planName"
              title="	计划名称"
              labelType={2}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
                placeholder: '请输入',
              }}
            />
            <YTHForm.Item
              name="inspectionMethod"
              title="巡检方式"
              labelType={2}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  return baseApi.getDictionary(dicParams.INSPECTION_METHOD);
                },
                disabled: type === 'view',
                p_props: {
                  placeholder: '请选择',
                },
              }}
            />
            <YTHForm.Item
              name="executionFrequency"
              title="执行频率"
              labelType={2}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  return baseApi.getDictionary(dicParams.EXECUTION_FREQUENCY);
                },
                disabled: type === 'view',
                p_props: {
                  placeholder: '请选择',
                },
              }}
            />
            <YTHForm.Item
              name="planType"
              title="计划类型"
              labelType={2}
              required
              componentName="Selector"
              componentProps={{
                request: async () => {
                  return baseApi.getDictionary(dicParams.PLAN_TYPE);
                },
                disabled: type === 'view',
                p_props: {
                  placeholder: '请选择',
                },
              }}
            />

            <YTHForm.Item
              name="directorUserName"
              title="负责人"
              labelType={2}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
                placeholder: '请输入',
              }}
            />
            <YTHForm.Item
              name="pointList"
              title="巡检路线"
              labelType={2}
              required
              component={locationPoint}
              componentProps={{
                disabled: type === 'view',
                operateType: type,
                radius: 200,
              }}
            />
            <YTHForm.Item
              name="pointUser"
              title="巡检人"
              labelType={2}
              required
              componentName="PickUser"
              componentProps={{
                defaultOrganize: {
                  id: CurrentUser()?.unitId,
                  name: '',
                  type: 'org',
                },
                requestOrganize: async () => {
                  const resData: Unit = await baseApi.getUnitTree();
                  return formatTree(resData, 'unitType', 'unitName');
                },
                requestUser: async (organize: Unit) => {
                  const resData: User[] = await baseApi.getUserList(organize.id);
                  const newUsers: User[] = resData?.map((item) => {
                    return { ...item, name: item.realName, type: 'user' };
                  });
                  return newUsers;
                },
                multiple: false,
                disabled: type === 'view',
              }}
            />
            <YTHForm.Item
              name="validDate"
              title="计划生效时间"
              required
              labelType={2}
              componentName="DatePicker"
              componentProps={{
                placeholder: '',
                range: true,
                precision: 'second',
                formatter: 'YYYY-MM-DD HH:mm:ss',
                disabled: type === 'view',
              }}
            />
            <YTHForm.Item
              name="taskDate"
              title="巡检计划时间"
              required
              labelType={2}
              componentName="DatePicker"
              componentProps={{
                placeholder: '',
                range: true,
                precision: 'second',
                formatter: 'YYYY-MM-DD HH:mm:ss',
                disabled: type === 'view',
              }}
            />
            <YTHForm.Item
              name="remark"
              title="备注"
              labelType={2}
              required
              componentName="Input"
              componentProps={{
                disabled: type === 'view',
              }}
            />
          </YTHForm>
        )}
        <div className={style['modal-operation']}>
          {(type === 'add' || type === 'edit') && (
            <Button onClick={save} type="primary">
              保存
            </Button>
          )}
          <Button onClick={cancel} className={style['reset-btn']}>
            取消
          </Button>
        </div>
      </Spin>
    </div>
  );
};
export default InspectionPlanModal;
