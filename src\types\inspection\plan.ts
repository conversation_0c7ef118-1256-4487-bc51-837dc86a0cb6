import { PageQueryParam, PageResponse } from '@/types/common';

/**
 * 计划检查点信息
 */
export interface InspectionPlanPointsVo {
  /** 主键 */
  id?: string;
  /** 计划id */
  planId?: string;
  /** 检查点名称 */
  pointName?: string;
  /** 检查点坐标 */
  pointLocation?: string;
  /** 巡检内容 */
  inspectionContent?: string;
  /** 排序 */
  sort?: number;
}

/**
 * 巡检计划信息
 */
export interface InspectionPlanVo {
  /** 主键 */
  id?: string;
  /** 计划编码 */
  planCode?: string;
  /** 计划名称 */
  planName?: string;
  /** 巡检方式 */
  inspectionMethod?: string;
  /** 计划类型 */
  planType?: string;
  /** 执行频率 */
  executionFrequency?: string;
  /** 执行频率间隔数(频率为天，间隔为2表示每2天执行一次) */
  frequencyNumber?: number;
  /** 负责人id */
  directorUserId?: string;
  /** 负责人姓名 */
  directorUserName?: string;
  /** 负责人联系电话 */
  directorPhone?: string;
  /** 计划生效时间 */
  validStartDate?: string;
  /** 计划失效时间 */
  validEndDate?: string;
  /** 任务开始时间 HH:mm 格式 */
  taskStartDate?: string;
  /** 任务结束时间 HH:mm 格式 */
  taskEndDate?: string;
  /** 线路点集合 */
  points?: string;
  /** 备注 */
  remark?: string;
  /** 参与巡检用户ids */
  partyUserIds?: string;
  /** 是否发送短信提醒(1:是 0:否) */
  isRemind?: number;
  /** 排序 */
  sort?: number;
  /** 是否即刻生成任务 */
  atOnceCreateTask?: boolean;
  /** 计划巡检点 */
  pointList?: InspectionPlanPointsVo[];
  [key: string]: unknown;
}

/**
 * 计划检查点查询参数
 */
export interface PlanQueryParamFilter {
  planName?: string;
  inspectionMethod?: [{ code: string; text: string }];
  planType?: [{ code: string; text: string }];
  planCode?: string;
  directorUserName?: string;
  executionFrequency?: [{ code: string; text: string }];
}

/**
 * 计划检查点查询参数
 */
export interface PlanQueryParam {
  planName?: string;
  inspectionMethod?: string;
  planType?: string;
  planCode?: string;
  directorUserName?: string;
  executionFrequency?: string;
}

/**
 * @description 新增参数
 * @param data 新增参数
 */
export interface PlanInsertParam extends InspectionPlanVo {
  id: string;
}
/**
 * @description 更新参数
 * @param data 更新参数
 */
export interface PlanUpdateParam extends InspectionPlanVo {
  id: string;
}
/**
 * @description 分页返回结果
 * @param data 分页查询参数
 */
export interface PlanPageResponse extends PageResponse<InspectionPlanVo> {
  data: InspectionPlanVo[];
}

/**
 * @description 分页查询参数
 * @param data 分页查询参数
 */
export interface PlanPageQueryParam extends PageQueryParam<PlanQueryParam> {
  condition: PlanQueryParam;
}
